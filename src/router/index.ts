import { createRouter, createWebHistory } from 'vue-router'
import Home from '../views/Home.vue'
import ProductDetail from '../views/ProductDetail.vue'
import Search from '../views/Search/Search.vue'
import SearchResult from '../views/Search/SearchResult.vue'
import ChooseAddress from '../views/User/ChooseAddress.vue'
import WarehouseAddress from '../views/User/WarehouseAddress.vue'
import Cart from '../views/Cart.vue'
import Browse from '../views/Browse.vue'
import Account from '../views/User/Account.vue'
import Setting from '../views/User/Setting.vue'
import Profile from '../views/User/Profile.vue'
import ReceiveInvitation from '../views/ReceiveInvitation/ReceiveInvitation.vue'
import ReceiveInvitationSuccess from '../views/ReceiveInvitation/ReceiveInvitationSuccess.vue'
import SelectLang from '@/views/Login/SelectLang.vue'
import PhoneRegister from '@/views/Login/PhoneRegister.vue'
import ConfirmOrder from '@/views/Order/ConfirmOrder.vue'
import ConfirmOrderSuccess from '@/views/Order/ConfirmOrderSuccess.vue'
import MyAddress from '@/views/User/MyAddress.vue'
import MyCoupon from '@/views/User/MyCoupon.vue'
import InviteRedeemCenter from '@/views/ReceiveInvitation/InviteRedeemCenter.vue'
import MyFavourite from '@/views/User/MyFavourite.vue'
import MyOrders from '@/views/User/MyOrders.vue'
import MembershipActivate from '@/views/Membership/MembershipActivate.vue'
import MembershipPayment from '@/views/Membership/MembershipPayment.vue'
import MembershipPolicy from '@/views/Membership/MembershipPolicy.vue'
import MembershipTransaction from '@/views/Membership/MembershipTransaction.vue'
import MembershipActivateHistory from '@/views/Membership/MembershipActivateHistory.vue'
import MembershipCancellationReason from '@/views/Membership/CancellationReason.vue'
import MembershipWithdraw from '@/views/Membership/Withdraw.vue'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      name: 'home',
      component: Home
      // component: () => import('@/views/Home.vue')
    },
    {
      path: '/product-detail/:id',
      name: 'product-detail',
      component: ProductDetail
      // component: () => import('@/views/ProductDetail.vue')
    },
    {
      path: '/search',
      name: 'search',
      component: Search
      // component: () => import('@/views/Search/Search.vue')
    },
    {
      path: '/search-result',
      name: 'search-result',
      component: SearchResult
      // component: () => import('@/views/Search/SearchResult.vue')
    },
    {
      path: '/search-view-all',
      name: 'SearchViewAll',
      component: () => import('@/views/Search/SearchViewAll.vue')
    },
    {
      path: '/choose-address',
      name: 'choose-address',
      component: ChooseAddress
      // component: () => import('@/views/User/ChooseAddress.vue')
    },
    {
      path: '/warehouse-address',
      name: 'warehouse-address',
      component: WarehouseAddress
      // component: () => import('@/views/User/WarehouseAddress.vue')
    },
    {
      path: '/browse',
      name: 'browse',
      component: Browse
      // component: () => import('@/views/Browse.vue')
    },
    {
      path: '/cart',
      name: 'cart',
      component: Cart
      // component: () => import('@/views/Cart.vue')
    },
    {
      path: '/account',
      name: 'account',
      component: Account
      // component: () => import('@/views/User/Account.vue')
    },
    {
      path: '/setting',
      name: 'setting',
      component: Setting
      // component: () => import('@/views/User/Setting.vue')
    },
    {
      path: '/profile',
      name: 'profile',
      component: Profile
      // component: () => import('@/views/User/Profile.vue')
    },
    {
      // 个人信息修改
      path: '/profile-info-change',
      name: 'profile-info-change',
      component: () => import('@/views/User/ProfileInfoChange.vue')
    },
    {
      // 受到邀请
      path: '/receive-invitation',
      name: 'ReceiveInvitation',
      component: ReceiveInvitation
      // component: () => import('@/views/ReceiveInvitation/ReceiveInvitation.vue')
    },
    {
      // 受到邀请-成功
      path: '/receive-invitation-success',
      name: 'ReceiveInvitationSuccess',
      component: ReceiveInvitationSuccess
      // component: () => import('@/views/ReceiveInvitation/ReceiveInvitationSuccess.vue')
    },
    {
      /* 选择语言 */
      path: '/select-lang',
      name: 'SelectLang',
      component: SelectLang
      // component: () => import('@/views/Login/SelectLang.vue')
    },
    {
      /* 手机号注册 */
      path: '/phone-register',
      name: 'PhoneRegister',
      component: PhoneRegister
      // component: () => import('@/views/Login/PhoneRegister.vue')
    },
    {
      /* 确认订单 */
      path: '/confirm-order',
      name: 'ConfirmOrder',
      component: ConfirmOrder
      // component: () => import('@/views/Order/ConfirmOrder.vue')
    },
    {
      // 下单成功
      path: '/confirm-order-success',
      name: 'ConfirmOrderSuccess',
      component: ConfirmOrderSuccess
      // component: () => import('@/views/Order/ConfirmOrderSuccess.vue')
    },
    {
      /* 我的支付方式 */
      path: '/my-payment-methods',
      name: 'MyPaymentMethods',
      component: () => import('@/views/User/MyPaymentMethods.vue')
    },
    {
      /* 我的地址 */
      path: '/my-address',
      name: 'MyAddress',
      component: MyAddress
      // component: () => import('@/views/User/MyAddress.vue')
    },
    {
      /* 我的优惠券 */
      path: '/my-coupon',
      name: 'MyCoupon',
      component: MyCoupon
      // component: () => import('@/views/User/MyCoupon.vue')
    },
    {
      /* 邀请和兑换中心 */
      path: '/invite-redeem-center',
      name: 'InviteRedeemCenter',
      component: InviteRedeemCenter
      // component: () => import('@/views/ReceiveInvitation/InviteRedeemCenter.vue')
    },
    {
      /* 我的收藏 */
      path: '/my-favourite',
      name: 'MyFavourite',
      component: MyFavourite
      // component: () => import('@/views/User/MyFavourite.vue')
    },
    {
      /* 我的订单 */
      path: '/my-orders',
      name: 'MyOrders',
      component: MyOrders
      // component: () => import('@/views/User/MyOrders.vue')
    },
    {
      /* 激活会员 */
      path: '/membership-activate',
      name: 'MembershipActivate',
      component: MembershipActivate
      // component: () => import('@/views/Membership/MembershipActivate.vue')
    },
    {
      /* 会员支付 */
      path: '/membership-payment',
      name: 'MembershipPayment',
      component: MembershipPayment
      // component: () => import('@/views/Membership/MembershipPayment.vue')
    },
    {
      /* 会员政策 */
      path: '/membership-policy',
      name: 'MembershipPolicy',
      component: MembershipPolicy
      // component: () => import('@/views/Membership/MembershipPolicy.vue')
    },
    {
      /* 钱包明细 */
      path: '/membership-transaction',
      name: 'MembershipTransaction',
      component: MembershipTransaction
      // component: () => import('@/views/Membership/MembershipTransaction.vue')
    },
    {
      /* 会员开通历史记录 */
      path: '/membership-activate-history',
      name: 'MembershipActivateHistory',
      component: MembershipActivateHistory
      // component: () => import('@/views/Membership/MembershipActivateHistory.vue')
    },
    {
      /* 会员取消原因 */
      path: '/membership-cancellation-reason',
      name: 'MembershipCancellationReason',
      component: MembershipCancellationReason
    },
    {
      /* 会员提现 */
      path: '/membership-withdraw',
      name: 'MembershipWithdraw',
      component: MembershipWithdraw
    }
  ],
  scrollBehavior(to, _, savedPosition) {
    const noScrollRoutes = ['home', 'MembershipActivate']

    if (noScrollRoutes.includes(to.name as string)) {
      return { top: savedPosition?.top || 0 }
    }

    return { top: 0 }
  }
})

export default router
