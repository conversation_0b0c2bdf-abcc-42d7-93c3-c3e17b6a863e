<template>
    <Loading v-if="loading" />
    <div
        v-else
        class="flex flex-col"
        :class="userStore.isMembership ? 'bg-custom-color-57' : 'bg-custom-color-49'">
        <van-sticky>
            <AppBar
                :title="userStore.isMembership ? 'UP+ Wallet' : 'Activate membership'"
                :titleStyle="userStore.isMembership ? 'color: #181818;' : 'color: #FFFFFF;'"
                :class="userStore.isMembership ? 'bg-custom-color-57' : 'bg-custom-color-49'">
                <template v-if="!userStore.isMembership" #left>
                    <SvgIcon
                        name="common-app-bar-left-white"
                        class="text-[24px] w-[18px]"
                        @click="handleBack"
                    ></SvgIcon>
                </template>
                <template #right>
                    <span
                        class="text-[17px] sf-pro-display-semibold"
                        :class="userStore.isMembership ? 'text-custom-color-black' : 'text-custom-color-white'"
                        @click="handleToMembershipPolicy"
                    >Policy</span>
                </template>
            </AppBar>
        </van-sticky>

        <!-- 会员 -->
        <div
            v-if="userStore.isMembership"
            class="bg-page-bg pt-[12px] px-[12px]"
            :class="!shoppingCategory || (shoppingCategory && Object.keys(shoppingCategory).length === 0) ? 'pb-[32px]' : 'pb-[12px]'">
            <!-- 会员信息 -->
            <MembershipInfo :data="userStore.userInfo?.membership" :showWithdraw="true" :showMore="true" />
            <!-- 交易记录 -->
            <TransactionBlock
                v-if="transaction && Object.keys(transaction).length > 0"
                class="mt-[10px]"
                :data="transaction"
                @click="handleToMembershipTransaction"
            />
            <!-- 购物分类 -->
            <ShoppingCategory v-if="shoppingCategory && Object.keys(shoppingCategory).length > 0" class="mt-[10px]" :data="shoppingCategory" />
        </div>
        <!-- 非会员 -->
        <div v-else class="flex flex-col items-center pt-[20px]">
            <div class="w-[400px] h-[152px] bg-[url('@/assets/images/membership/est-savings-card-bg.png')] rounded-t-[25px] py-[17px] px-[16px] flex flex-col">
                <div class="flex items-end">
                    <div
                        class="text-[26px] sf-pro-display-bold leading-none gradient-text"
                        style="background: linear-gradient(172.05deg, #FFECC4 -20.02%, #FFD48C 118.31%);"
                    >{{ activateBenefitsDesc?.title.first_character }}</div>
                    <ColorSegmentedText
                        v-if="activateBenefitsDesc"
                        :text="activateBenefitsDesc.title.title.replace(activateBenefitsDesc.title.first_character, '')"
                        :keyword="activateBenefitsDesc.title.amount"
                        class="text-[16px] sf-pro-display-semibold leading-[20px]"
                        textClass="text-custom-color-77"
                        keywordClass="text-custom-color-6"
                    />
                    <img src="@/assets/images/membership/up-plus-gold-rimmed.png" alt="membership" class="w-[97.84px] h-[35px] object-contain ml-auto">
                </div>
                <ColorSegmentedText
                    v-if="activateBenefitsDesc"
                    :text="activateBenefitsDesc.sub_title.title"
                    :keyword="activateBenefitsDesc.sub_title.amount"
                    class="text-[14px] sf-pro-display-medium leading-[20px] mt-[10px]"
                    textClass="text-custom-color-7"
                    keywordClass="text-custom-color-6"
                />
                <ColorSegmentedText
                    v-if="activateBenefitsDesc"
                    :text="activateBenefitsDesc.desc.title"
                    :keyword="activateBenefitsDesc.desc.amount"
                    class="text-[20px] sf-pro-display-bold leading-none mt-[3px]"
                    textClass="text-custom-color-47"
                    keywordClass="text-custom-color-6"
                />
            </div>
        </div>

        <!-- 会员权益 -->
        <div class="min-h-[644px] bg-[url('@/assets/images/membership/benefits-bg.png')] mt-[-28px] flex flex-col items-center">
            <!-- 会员权益标题 -->
            <div class="mt-[45px] mx-[24px] mb-[23px] flex items-center">
                <div class="w-[52px] h-[2px] mr-[10px]" style="background: linear-gradient(90deg, rgba(247, 209, 144, 0.5) 0%, #F1B664 100%);"></div>
                <div class="text-custom-color-black text-[17px] sf-pro-display-bold">{{ benefits.title }}</div>
                <div class="w-[52px] h-[2px] ml-[10px]" style="background: linear-gradient(270deg, rgba(247, 209, 144, 0.5) 0%, #F1B664 100%);"></div>
            </div>
            <!-- 会员权益列表 -->
            <div class="flex flex-col items-center">
                <div
                    v-for="benefit in benefits.elaborate"
                    :key="benefit.title"
                    class="w-[356px] min-h-[112px] bg-custom-color-52 rounded-[15px] mb-[15px] px-[20px] flex items-center">
                    <img
                        :src="benefit.icon || benefitsIcon(benefit.tag)"
                        alt="benefit"
                        class="w-[44px] h-[44px] object-contain mr-[18px]"
                    >
                    <div class="flex flex-col">
                        <div class="text-custom-color-black text-[18px] sf-pro-display-bold">{{ benefit.title }}</div>
                        <div class="text-custom-color-black text-[15px] sf-pro-display-medium">{{ benefit.desc }}</div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 权益详情 -->
        <div class="rounded-t-[20px] bg-custom-color-10 mt-[-30px] pt-[30px] pb-[51px] flex flex-col items-center gap-y-[24px]">
            <!-- 权益详情标题 -->
            <div class="mx-[24px] flex items-center">
                <div class="w-[52px] h-[2px] mr-[10px]" style="background: linear-gradient(90deg, rgba(247, 209, 144, 0.5) 0%, #F1B664 100%);"></div>
                <div class="text-custom-color-black text-[17px] sf-pro-display-bold">{{ benefitDetails?.title || 'Benefit Details' }}</div>
                <div class="w-[52px] h-[2px] ml-[10px]" style="background: linear-gradient(270deg, rgba(247, 209, 144, 0.5) 0%, #F1B664 100%);"></div>
            </div>
            <template v-for="item in benefitDetails?.list" :key="item.type">
                <!-- 权益详情(带商品)列表 -->
                <div v-if="'products' in item" class="w-[100vw] flex flex-col">
                    <div class="px-[10px] flex justify-between">
                        <div class="flex flex-col">
                            <span class="text-custom-color-2 text-[18px] sf-pro-display-bold">{{ item.title }}</span>
                            <span class="text-custom-color-56 text-[14px] sf-pro-display-regular">{{ item.desc }}</span>
                        </div>
                        <div class="w-[119px] h-[54px] rounded-t-[15px] rounded-bl-[15px] rounded-br-[2px] px-[10px] flex flex-col items-end justify-center" style="background: linear-gradient(90deg, #FFF4D8 -4.41%, #FFE4B1 114.22%);">
                            <span class="text-custom-color-black text-[18px] sf-pro-display-bold">{{ item.savings_amount }}</span>
                            <span class="text-custom-color-black text-[12px] sf-pro-display-regular">{{ item.savings_desc }}</span>
                        </div>
                    </div>
                    <!-- 商品列表 -->
                    <div class="mt-[12px] px-[10px] flex flex-nowrap shrink-0 gap-x-[8px] overflow-auto scrollbar-hidden">
                        <div class="w-[125px] bg-custom-color-white rounded-[10px] flex flex-col shrink-0" v-for="product in item.products" :key="product.id">
                            <img
                                class="w-full h-[125px] rounded-t-[16px] object-cover"
                                :src="product.image"
                                alt=""
                                srcset=""
                            >
                            <div class="px-[6px] pt-[6.28px] pb-[12.44px] flex flex-col items-start">
                                <div class="h-[40px] text-two-lines-ellipsis text-custom-color-2 text-[13px] sf-pro-display-medium">{{ product.name }}</div>
                                <div class="w-full flex items-center mt-[6px]">
                                    <span
                                        class="text-[15px] sf-pro-display-semibold"
                                        :class="item.type === 'unlimited_cashback' ? 'text-custom-color-6' : 'text-custom-color-2'"
                                    >{{ product.need_weight === 1 ? product.price_info.unit_member_price + ' / ' + product.weight_unit : product.price_info.member_price }}</span>
                                    <img
                                        src="@/assets/images/membership/cart.png"
                                        alt="cart"
                                        class="w-[26px] h-[26px] object-contain ml-auto"
                                        @click="router.push(`/product-detail/${product.id}`)"
                                    >
                                </div>
                                <div v-if="item.type === 'unlimited_cashback'" class="bg-[#FFECC4] rounded-[2.51px] p-[2px] text-custom-color-2 text-[12px] mt-[6px]">
                                    <span class="sf-pro-display-semibold">{{ product.est_saving_desc }}</span>
                                </div>
                                <div v-else class=" text-custom-color-28 text-[15px] sf-pro-display-medium line-through mt-[6px]">
                                    <span>{{ product.need_weight === 1 ? product.price_info.unit_price : product.price_info.price }}</span>
                                    <!-- <span v-if="product.need_weight === 1">/{{ product.weight_unit }}</span> -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 权益详情(不带商品)列表 -->
                <div v-else class="w-[100vw] px-[10px] flex flex-col">
                    <div class="text-custom-color-black text-[18px] sf-pro-display-bold leading-[20px]">{{ item.title }}</div>
                    <div class="text-custom-color-56 text-[14px] sf-pro-display-regular leading-none mt-[6px]">{{ item.desc }}</div>
                    <div
                        class="mt-[19px] rounded-[15px] flex flex-row items-center py-[22px]"
                        style="background: linear-gradient(0deg, #2F2C26 0%, #181818 100%);">
                        <img
                            :src="benefitDetailsIcon(item.type)"
                            alt=""
                            class="w-[50px] h-[50px] object-contain ml-[19.5px]"
                        >
                        <div class="w-[1px] h-[62px] bg-[#8F702E80] ml-[15px]"></div>
                        <div class="flex flex-col ml-[16px] pr-[25px]">
                            <div class="text-custom-color-44 text-[16px] sf-pro-display-semibold leading-[20px]">{{ item.detail.title }}</div>
                            <div class="text-[#FADA93B2] text-[14px] sf-pro-display-medium leading-[20px] mt-[7px]">{{ item.detail.desc }}</div>
                        </div>
                    </div>
                </div>
            </template>
        </div>
        <!-- 激活按钮占位 -->
        <div v-if="!userStore.isMembership && activateTips" class="w-full h-[180px] bg-custom-color-white"></div>
        <!-- 激活按钮 -->
        <div
            v-if="!userStore.isMembership && activateTips"
            class="fixed bottom-0 left-0 right-0 bg-custom-color-white rounded-t-[20px] mt-[-20px] px-[30px] pt-[20px] pb-[61px] flex flex-col items-center"
            style="box-shadow: 2px 0px 10px 2px #9A8A6E1A;">
            <div class="text-custom-color-black text-[15px] sf-pro-display-medium leading-[20px]">{{ activateTips.title || 'Renew automatically, cancel anytime.' }}</div>
            <div
                class="bg-[url('@/assets/images/membership/activate-button-bg.png')] bg-no-repeat bg-contain bg-center w-[370px] h-[60px] mt-[16px] flex"
                @click="handleActivate">
                <div class="bg-[url('@/assets/images/membership/activate-button-price-bg.png')] bg-no-repeat bg-contain bg-center w-[260.19px] h-[60px] flex items-center justify-center">
                    <div class="flex items-end ml-[-30px]">
                        <!-- 现价 -->
                        <!-- <span class="text-custom-color-45 text-[18px] sf-pro-display-regular">$</span> -->
                        <span class="text-custom-color-45 text-[26px] sf-pro-display-medium leading-[83%]">{{ activateTips.activate_price_show }}</span>
                        <!-- <span class="text-custom-color-45 text-[18px] sf-pro-display-regular leading-none">/{{ activateTips.activate_unit }}</span> -->
                        <!-- 原价 -->
                        <span
                            v-if="activateTips.activate_org_price_show != activateTips.activate_price_show"
                            class="text-custom-color-48 text-[18px] sf-pro-display-regular leading-none line-through ml-[10px]"
                        >{{ activateTips.activate_org_price_show }}</span>
                    </div>
                </div>
                <div class="text-custom-color-black text-[20px] sf-pro-display-medium text-center leading-[58px]">Activate</div>
            </div>
            <div class="mt-[12px] flex">
                <van-checkbox class="membership-checkbox" v-model="termsChecked"></van-checkbox>
                <span class="text-custom-color-7 text-[14px] sf-pro-display-regular ml-[8px]">Read and agree to the </span>
                <span class="text-custom-color-46 text-[14px] sf-pro-display-regular">&nbsp;UP+ Service Terms</span>
                <span class="text-custom-color-7 text-[14px] sf-pro-display-regular">.</span>
            </div>
        </div>

        <!-- 同意协议弹窗（不要放到外面让template有两个根节点，会导致App.vue的deep样式失效） -->
        <TermsAgreePopup v-if="termsAgreePopupData" ref="termsAgreePopupRef" :data="termsAgreePopupData" @agree="handleTermsAgree" />
    </div>
</template>

<script setup lang="ts">
import { ref, onActivated } from 'vue'
import { showToast, showLoadingToast } from 'vant'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { membershipService } from '@/api/membership'
import type {
    ActivateBenefitsBenefitDetails,
    // ActivateBenefitsTitle,
    ActivateBenefitsDesc,
    ActivateBenefitsTransaction,
    ActivateBenefitsShoppingCategory,
    ActivateBenefitsActivateTips,
    ActivateBenefitsCheck
} from '@/types/membership'
import Loading from '@/components/Membership/Loading.vue'
import MembershipInfo from '@/components/Membership/MembershipInfo.vue'
import TransactionBlock from '@/components/Membership/TransactionBlock.vue'
import ShoppingCategory from '@/components/Membership/ShoppingCategory.vue'
import ColorSegmentedText from '@/components/Common/ColorSegmentedText.vue'
import TermsAgreePopup from '@/components/Membership/TermsAgreePopup.vue'

const loading = ref(true)
const router = useRouter()
const route = useRoute()
const userStore = useUserStore()
// const activateBenefitsTitle = ref<ActivateBenefitsTitle>()
const activateBenefitsDesc = ref<ActivateBenefitsDesc>()
const transaction = ref<ActivateBenefitsTransaction>()
const shoppingCategory = ref<ActivateBenefitsShoppingCategory>()
const benefitsIcon = (key: string) => {
    switch (key) {
        case 'Unlimited Cashback':
        case 'Unlimited cashback':
        case 'unlimited cashback':
        case 'cashback':
            return new URL('@/assets/images/membership/benefits-unlimited-cashback.png', import.meta.url).href
        case 'Member-Exclusive Price':
        case 'Member-Exclusive price':
        case 'Member-exclusive price':
        case 'member-exclusive price':
        case 'exclusive_price':
            return new URL('@/assets/images/membership/benefits-member-exclusive-price.png', import.meta.url).href
        case 'Free Returns':
        case 'Free returns':
        case 'free returns':
        case 'free_return':
            return new URL('@/assets/images/membership/benefits-free-returns.png', import.meta.url).href
        case 'Hassle-Free Cancellation':
        case 'Hassle-Free cancellation':
        case 'Hassle-free cancellation':
        case 'hassle-free cancellation':
        case 'cancel_anytime':
            return new URL('@/assets/images/membership/benefits-hassle-free-cancellation.png', import.meta.url).href
        default:
            return ''
    }
}
const benefits = ref({
    title: '4 Exclusive Membership Benefits',
    elaborate: [
        {
            title: 'Unlimited cashback',
            desc: 'Earn 2% cashback on every order with NO limits!',
            icon: '',
            tag: 'cashback'
        },
        {
            title: 'Member-Exclusive Price',
            desc: 'Unlock exclusive VIP prices—Shop more, Save More!',
            icon: '',
            tag: 'exclusive_price'
        },
        {
            title: 'Free Returns',
            desc: 'Enjoy hassle-free 90-day returns—No Questions Asked!',
            icon: '',
            tag: 'free_return'
        },
        {
            title: 'Hassle-Free Cancellation',
            desc: 'Cancel anytime and get a full refund for the current month—no hassle, no risk!',
            icon: '',
            tag: 'cancel_anytime'
        }
    ]
})
const benefitDetailsIcon = (key: string) => {
    switch (key) {
        case 'Unlimited Cashback':
        case 'Unlimited cashback':
        case 'unlimited cashback':
        case 'cashback':
            return new URL('@/assets/images/membership/benefits-cashback.png', import.meta.url).href
        case 'Member-Exclusive Price':
        case 'Member-Exclusive price':
        case 'Member-exclusive price':
        case 'member-exclusive price':
        case 'exclusive_price':
            return new URL('@/assets/images/membership/benefits-discount.png', import.meta.url).href
        case 'Free Returns':
        case 'Free returns':
        case 'free returns':
        case 'free_return':
            return new URL('@/assets/images/membership/benefits-returns.png', import.meta.url).href
        case 'Hassle-Free Cancellation':
        case 'Hassle-Free cancellation':
        case 'Hassle-free cancellation':
        case 'hassle-free cancellation':
        case 'hassle_free_cancellation':
        case 'cancel_anytime':
            return new URL('@/assets/images/membership/benefits-cancellation.png', import.meta.url).href
        default:
            return ''
    }
}
const benefitDetails = ref<ActivateBenefitsBenefitDetails>()
const activateTips = ref<ActivateBenefitsActivateTips>()
const termsChecked = ref(true)
const termsAgreePopupRef = ref<InstanceType<typeof TermsAgreePopup>>()
const termsAgreePopupData = ref<ActivateBenefitsCheck>()

// 获取激活权益
const getActivateBenefits = async () => {
    try {
        loading.value = true
        const res = await membershipService.getMembershipWallet()
        console.log("[MembershipActivate.vue][getActivateBenefits]res:", res)
        if (res.status === 1) {
            /* if (res.data.activate_benefits_title?.title && res.data.activate_benefits_title?.replace_str) {
                const title = res.data.activate_benefits_title.title
                const replaceStr = res.data.activate_benefits_title.replace_str
                const titleArray = title.split(replaceStr).reduce((acc: string[], cur, index, array) => {
                    if (index < array.length - 1) {
                        acc.push(cur, replaceStr)
                    } else {
                        acc.push(cur)
                    }
                    return acc
                }, [])
                activateBenefitsTitle.value = {
                    title,
                    replace_str: replaceStr,
                    title_array: titleArray
                }
                console.log("[MembershipActivate.vue][getActivateBenefits]activateBenefitsTitle:", activateBenefitsTitle.value)
            } */
            if (res.data.activate_benefits_desc) {
                activateBenefitsDesc.value = res.data.activate_benefits_desc
            }
            if (res.data.transaction) {
                transaction.value = res.data.transaction
            }
            if (res.data.shopping_category) {
                shoppingCategory.value = res.data.shopping_category
            }
            if (res.data.membership_benefits) {
                benefits.value = res.data.membership_benefits
            }
            if (res.data.benefit_details) {
                benefitDetails.value = res.data.benefit_details
            }
            if (res.data.activate_tips) {
                activateTips.value = res.data.activate_tips
            }
            if (res.data.check) {
                let descArray = [res.data.check.desc]
                if (res.data.check.desc && res.data.check.replace_str) {
                    descArray = res.data.check.desc.split(res.data.check.replace_str).reduce((acc: string[], cur, index, array) => {
                        if (index < array.length - 1) {
                            acc.push(cur, res.data.check.replace_str)
                        } else {
                            acc.push(cur)
                        }
                        return acc
                    }, [])
                }
                termsAgreePopupData.value = {
                    title: res.data.check.title,
                    desc: res.data.check.desc,
                    desc_array: descArray,
                    replace_str: res.data.check.replace_str,
                    event_url: res.data.check.event_url
                }
            }
            loading.value = false
            return true
        } else {
            console.log(res.error)
            showToast(res.error || '获取会员数据失败')
        }
    } catch (error) {
        console.error(error)
        showToast((error as string) || '获取会员数据失败')
    }
}
onActivated(async () => {
    // console.log("[MembershipActivate.vue][onActivated]")
    if (route.query.refresh === 'true') {
        if (!userStore.isLogin) {
            router.replace('/phone-register')
        } else {
            const res = await getActivateBenefits()
            if (res && import.meta.env.VITE_USER_NODE_ENV != 'development') {
                const newQuery = { ...route.query }
                delete newQuery.refresh
                router.replace({ path: route.path, query: newQuery })
            }
        }
    }
})

// 返回
const handleBack = () => {
    router.back()
}

const handleActivate = () => {
    if (termsChecked.value) {
        handleTermsAgree()
    } else {
        handleTermsAgreePopupShow()
    }
}

const handleTermsAgreePopupShow = () => {
    // console.log("[MembershipActivate.vue][handleTermsAgreePopupShow]termsAgreePopupData:", termsAgreePopupData.value)
    termsAgreePopupRef.value?.open()
}
const handleTermsAgreePopupClose = () => {
    termsAgreePopupRef.value?.close()
}

const handleTermsAgree = () => {
    // console.log("[MembershipActivate.vue][handleTermsAgree]");
    // termsChecked.value = true
    // setTimeout(() => {
        router.push('/membership-payment')
        handleTermsAgreePopupClose()
    // }, 200)
}

const handleToMembershipPolicy = () => {
    router.push('/membership-policy')
}

// 跳转钱包明细
const handleToMembershipTransaction = () => {
    router.push('/membership-transaction')
}
</script>

<style scoped>
</style>