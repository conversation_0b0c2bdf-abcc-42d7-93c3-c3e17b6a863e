<template>
    <div class="bg-custom-color-57 h-screen flex flex-col">
        <AppBar title="Withdraw">
            <template #right>
                <span
                    class="text-[17px] sf-pro-display-semibold text-custom-color-black"
                    @click="handleToMembershipPolicy"
                >Policy</span>
            </template>
        </AppBar>

        <div class="flex-1 p-[12px] flex flex-col">
            <!-- 钱包卡片 -->
            <div
                class="rounded-[20px] p-[20px]"
                style="background: linear-gradient(360deg, #302920 33.25%, #000000 100%);">
                <div class="text-[#FBDD9799] text-[14px] sf-pro-display-regular">
                    <span>{{ userStore.userInfo?.membership?.expiration_date_text || '' }}</span>
                </div>
                <div class="flex items-center mt-[14px]">
                    <img class="w-[80px] h-[30.13px] object-contain" src="@/assets/images/membership/up-plus.png" alt="">
                </div>
                <!-- 钱包余额信息 -->
                <div class="flex justify-center mt-[20px]">
                    <div class="flex flex-col items-center">
                        <div class="text-custom-color-54 text-[13px] sf-pro-display-medium">Available Credit</div>
                        <div class="text-custom-color-white text-[30px] sf-pro-display-semibold">{{ userStore.userInfo?.membership?.available_balance_show || '$0.00' }}</div>
                    </div>
                </div>
            </div>

            <!-- 提现表单 -->
            <div class="flex-1 bg-custom-color-white rounded-[10px] mt-[10px] py-[22px] px-[20px]">
                <div class="text-custom-color-2 text-[20px] sf-pro-display-bold leading-none">Withdraw</div>
                
                <div class="mt-[20px]">
                    <div class="text-custom-color-2 text-[15px] sf-pro-display-medium mb-[6px]">Amount</div>
                    <van-field
                        v-model="withdrawAmount"
                        type="number"
                        placeholder="$0.00"
                        class="bg-gray-100 rounded-[8px]"
                        :rules="[{ required: true, message: 'Amount is required' }]"
                        @blur="formatAmount"
                    >
                        <template #prefix>
                            <span class="text-custom-color-2 ml-[4px]">$</span>
                        </template>
                    </van-field>
                    
                    <div class="flex justify-between mt-[8px]">
                        <div class="text-custom-color-55 text-[13px] sf-pro-display-regular">Min $50.00</div>
                        <div 
                            class="text-custom-color-2 text-[13px] sf-pro-display-medium"
                            @click="withdrawAll"
                        >Withdraw All</div>
                    </div>
                </div>

                <div class="mt-[24px]">
                    <div class="text-custom-color-2 text-[15px] sf-pro-display-medium mb-[6px]">Withdraw to</div>
                    <div class="bg-gray-100 rounded-[8px] p-[12px] flex justify-between items-center">
                        <div class="flex items-center">
                            <SvgIcon name="membership-card" class="w-[24px] h-[24px] mr-[8px]" />
                            <span class="text-custom-color-2 text-[15px] sf-pro-display-medium">****{{ userStore.userInfo?.card_last_four || '9092' }}</span>
                        </div>
                        <SvgIcon name="membership-check" class="w-[20px] h-[20px]" />
                    </div>
                </div>

                <div class="mt-auto pt-[40px]">
                    <van-button
                        block
                        type="primary"
                        class="h-[48px] rounded-[24px] bg-custom-color-2 border-custom-color-2"
                        :disabled="!isValid"
                        @click="handleWithdraw"
                    >
                        <span class="text-white text-[16px] sf-pro-display-semibold">Request Withdrawal</span>
                    </van-button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { membershipService } from '@/api/membership'
import { showLoadingToast, showToast } from 'vant'

const router = useRouter()
const userStore = useUserStore()
const loading = ref<any>(null)
const withdrawAmount = ref('')
const availableBalance = ref(0)

// 获取可用余额数值（不带$符号）
const getAvailableBalanceNumber = () => {
    const balanceText = userStore.userInfo?.membership?.available_balance_show || '$0.00'
    return parseFloat(balanceText.replace('$', '').replace(',', ''))
}

// 计算提现是否有效
const isValid = computed(() => {
    const amount = parseFloat(withdrawAmount.value || '0')
    return amount >= 50 && amount <= availableBalance.value
})

// 格式化金额
const formatAmount = () => {
    if (withdrawAmount.value) {
        const amount = parseFloat(withdrawAmount.value)
        if (amount > availableBalance.value) {
            withdrawAmount.value = availableBalance.value.toString()
            showToast('Amount exceeds available balance')
        } else if (amount < 50) {
            showToast('Minimum withdrawal amount is $50.00')
        }
    }
}

// 提现全部
const withdrawAll = () => {
    withdrawAmount.value = availableBalance.value.toString()
}

// 初始化数据
const initData = async () => {
    try {
        loading.value = showLoadingToast({ duration: 0, forbidClick: true })
        
        // 如果需要重新获取会员信息
        if (!userStore.userInfo?.membership) {
            await userStore.getUserInfo()
        }
        
        availableBalance.value = getAvailableBalanceNumber()
        
        loading.value?.close()
        loading.value = null
    } catch (error) {
        console.error(error)
        showToast((error as string) || 'Failed to load data')
        loading.value?.close()
        loading.value = null
    }
}

// 处理提现请求
const handleWithdraw = async () => {
    try {
        if (!isValid.value) {
            return
        }

        loading.value = showLoadingToast({ duration: 0, forbidClick: true, message: 'Processing...' })
        
        // TODO: 这里需要实现调用提现API的逻辑
        // 目前API服务未提供，可以先模拟成功
        
        setTimeout(() => {
            loading.value?.close()
            loading.value = null
            showToast('Withdrawal request submitted successfully')
            // 跳转到交易记录页面
            router.push('/membership-transaction')
        }, 1500)
        
    } catch (error) {
        console.error(error)
        showToast((error as string) || 'Withdrawal failed')
        loading.value?.close()
        loading.value = null
    }
}

// 跳转到会员政策页面
const handleToMembershipPolicy = () => {
    router.push('/membership-policy')
}

onMounted(() => {
    initData()
})
</script>

<style scoped>
:deep(.van-field__control) {
    height: 42px;
    font-size: 16px;
    font-family: 'SF Pro Display';
    font-weight: 500;
    color: var(--custom-color-2);
}

:deep(.van-button--primary) {
    background-color: var(--custom-color-2);
    border-color: var(--custom-color-2);
}
</style>