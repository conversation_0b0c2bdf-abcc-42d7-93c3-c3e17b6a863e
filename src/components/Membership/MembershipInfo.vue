<template>
    <div
        class="rounded-[20px] p-[20px]"
        style="background: linear-gradient(360deg, #302920 33.25%, #000000 100%);">
        <div class="text-[#FBDD9799] text-[14px] sf-pro-display-regular">
            <span>{{ props.data.expiration_date_text }}</span>
        </div>
        <div class="flex items-center mt-[14px]">
            <template v-if="props.showWithdraw">
                <img class="w-[80px] h-[30.13px] object-contain" src="@/assets/images/membership/up-plus.png" alt="">
            </template>
            <template v-else>
                <img class="w-[43.94px] h-[16.55px] object-contain mr-[7.64px]" src="@/assets/images/membership/up-plus.png" alt="">
                <div class="text-custom-color-54 text-[22.91px] sf-pro-display-semibold leading-none">Wallet</div>
            </template>
            <div
                v-if="props.showWithdraw"
                class="w-[85px] h-[32px] rounded-[100px] bg-[#FBDD971A] text-custom-color-54 text-[14px] sf-pro-display-regular leading-[32px] text-center ml-auto"
                @click="router.push('/membership-withdraw')"
            >Withdraw</div>
            <SvgIcon v-else name="membership-right-arrow-golden" class="w-[8px] h-[14.25px] ml-auto" @click="handleToMembershipActivate"></SvgIcon>
        </div>
        <!-- 钱包相关 -->
        <div class="flex justify-between mt-[20px]">
            <div v-if="userStore.isMembershipExpired" class="flex flex-col items-center">
                <div class="text-custom-color-54 text-[13px] sf-pro-display-medium">Coupons</div>
                <div class="text-custom-color-white text-[22px] sf-pro-display-medium">{{ userStore.userInfo?.coupons }}</div>
            </div>
            <div v-else class="flex flex-col items-center">
                <div class="text-custom-color-54 text-[13px] sf-pro-display-medium">Total Savings</div>
                <div class="text-custom-color-white text-[22px] sf-pro-display-medium">{{ props.data.total_savings_show }}</div>
            </div>
            <div class="flex flex-col items-center ml-[-20px]">
                <div class="text-custom-color-54 text-[13px] sf-pro-display-medium">Available Balance</div>
                <div class="text-custom-color-white text-[22px] sf-pro-display-medium">{{ props.data.available_balance_show }}</div>
            </div>
            <div class="flex flex-col items-center">
                <div class="text-custom-color-54 text-[13px] sf-pro-display-medium">Pending</div>
                <div class="text-custom-color-white text-[22px] sf-pro-display-medium">{{ props.data.pending_cashback_show }}</div>
            </div>
        </div>
        <!-- 续费相关 -->
        <div
            v-if="props.showMore && !userStore.isMembershipExpired"
            class="rounded-[10px] overflow-hidden mx-[-11px]"
            style="transition: all 0.5s ease-in-out;"
            :style="{ 'max-height': renewalBlockHeight }">
            <div
                class="mt-[24px] px-[14px] py-[16px] flex flex-col gap-y-[24px]"
                style="background: linear-gradient(180deg, #000000 0%, #181818 100%);">
                <div class="flex">
                    <span class="text-custom-color-4 text-[15px] sf-pro-display-i">Auto-Debit Payment</span>
                    <span class="text-custom-color-white text-[14px] sf-pro-display-semibold ml-auto">
                        {{ props.data.auto_renewal === 1 ? 'Cancel' : 'Activate' }}
                    </span>
                </div>
                <div class="flex items-center">
                    <span class="text-custom-color-4 text-[15px] sf-pro-display-i">Auto-Debit Payment Method</span>
                    <span class="text-custom-color-white text-[14px] sf-pro-display-semibold ml-auto">{{ props.data.auto_debit_payment_method }}</span>
                    <SvgIcon name="membership-right-arrow-white" class="w-[8px] h-[14.25px] ml-[6px]"></SvgIcon>
                </div>
                <div class="flex items-center" @click="router.push('/membership-activate-history')">
                    <span class="text-custom-color-4 text-[15px] sf-pro-display-i">Est. Renewal Date</span>
                    <span class="text-custom-color-white text-[14px] sf-pro-display-semibold ml-auto">{{ props.data.est_renewal_date }}</span>
                    <SvgIcon name="membership-right-arrow-white" class="w-[8px] h-[14.25px] ml-[6px]"></SvgIcon>
                </div>
                <div class="flex">
                    <span class="text-custom-color-4 text-[15px] sf-pro-display-i">Next Renewal Amount</span>
                    <span class="text-custom-color-white text-[14px] sf-pro-display-semibold ml-auto">{{ props.data.next_renewal_amount_show }}</span>
                </div>
            </div>
        </div>
        <!-- 展开续费相关信息箭头 -->
        <div v-if="props.showMore && !userStore.isMembershipExpired" class="flex justify-center">
            <!-- 收起 -->
            <SvgIcon v-if="renewalBlockHeight !== '0'" name="membership-right-arrow-golden" class="w-[6px] h-[12px] rotate-[270deg] mt-[9.13px] mb-[-8px]" @click="handleRenewalBlockExpandCollapse"></SvgIcon>
            <!-- 展开 -->
            <SvgIcon v-else name="membership-right-arrow-golden" class="w-[6px] h-[12px] rotate-[90deg] mt-[9.13px] mb-[-8px]" @click="handleRenewalBlockExpandCollapse"></SvgIcon>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()
const props = defineProps<{
    data: any
    showWithdraw?: boolean
    showMore?: boolean
}>()
const renewalBlockHeight = ref('0')

const handleRenewalBlockExpandCollapse = () => {
    renewalBlockHeight.value = renewalBlockHeight.value === '0' ? '300rem' : '0'
}

const handleToMembershipActivate = () => {
    router.push({
        path: '/membership-activate',
        query: {
            refresh: 'true'
        }
    })
}
</script>

<style scoped>

</style>